using FY.WB.CSHero2.Domain.Entities;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore; // Added for IgnoreQueryFilters
using FY.WB.CSHero2.Domain.Entities.Core; // For PaymentMethodInfo and BillingAddressInfo if they are there

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public class DataSeederLogger {}

    public static class DataSeeder
    {
        // DTO for deserializing tenant-profiles.json
        public class TenantProfileSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Company { get; set; } = string.Empty;
            public string Subscription { get; set; } = string.Empty;
            public DateTime LastLoginTime { get; set; }
            public string BillingCycle { get; set; } = string.Empty;
            public DateTime NextBillingDate { get; set; }
            public string SubscriptionStatus { get; set; } = string.Empty;
            public PaymentMethodInfo? PaymentMethod { get; set; } // As object
            public BillingAddressInfo? BillingAddress { get; set; } // As object
            public Guid? TenantId { get; set; }
            // Add CreationTime and LastModificationTime if they are in JSON and need to be preserved
            // For now, assuming they will be set by the application logic or default to UtcNow
        }

        // DTO for deserializing templates.json
        public class TemplateSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public string ThumbnailUrl { get; set; } = string.Empty;
            public List<string>? Tags { get; set; } // As collection
            public List<TemplateSection>? Sections { get; set; } // As collection
            public List<TemplateField>? Fields { get; set; } // As collection
            public Guid? TenantId { get; set; }
        }

        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true // Makes the stored JSON more readable
        };

        // DTO classes for JSON deserialization
        private class TenantProfileDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Company { get; set; } = string.Empty;
            public string Subscription { get; set; } = string.Empty;
            public DateTime LastLoginTime { get; set; }
            public string BillingCycle { get; set; } = string.Empty;
            public DateTime NextBillingDate { get; set; }
            public string SubscriptionStatus { get; set; } = string.Empty;
            public PaymentMethodDto? PaymentMethod { get; set; }
            public BillingAddressDto? BillingAddress { get; set; }
            public Guid? TenantId { get; set; }
            public DateTime CreationTime { get; set; } = DateTime.UtcNow;
            public DateTime? LastModificationTime { get; set; } = DateTime.UtcNow;
        }

        private class ClientDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string CompanyName { get; set; } = string.Empty;
            public string? Phone { get; set; }
            public string? Address { get; set; }
            public string? CompanySize { get; set; }
            public string? Industry { get; set; }
            public DateTime CreatedAt { get; set; } // Maps to CreationTime
            public DateTime UpdatedAt { get; set; } // Maps to LastModificationTime
            public Guid? TenantId { get; set; }
        }

        private class ReportDto
        {
            public Guid Id { get; set; }
            public string ReportNumber { get; set; } = string.Empty;
            public Guid ClientId { get; set; }
            public string ClientName { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public int SlideCount { get; set; }
            public string Status { get; set; } = string.Empty;
            public string Author { get; set; } = string.Empty;
            public Guid? TenantId { get; set; }
            public DateTime CreationTime { get; set; } = DateTime.UtcNow;
            public DateTime? LastModificationTime { get; set; } = DateTime.UtcNow;
        }

        private class PaymentMethodDto
        {
            public string CardType { get; set; } = string.Empty;
            public string LastFourDigits { get; set; } = string.Empty;
            public string ExpirationDate { get; set; } = string.Empty;
            public string SecurityMethod { get; set; } = string.Empty;
        }

        private class BillingAddressDto
        {
            public string Street { get; set; } = string.Empty;
            public string City { get; set; } = string.Empty;
            public string State { get; set; } = string.Empty;
            public string ZipCode { get; set; } = string.Empty;
            public string Country { get; set; } = string.Empty;
        }

        private static async Task<List<T>> ReadJsonData<T>(string fileName, ILogger logger)
        {
            var path = FindSeedDataFile(fileName, logger);
            if (string.IsNullOrEmpty(path))
            {
                logger.LogWarning("Seed data file not found: {FileName}", fileName);
                return new List<T>();
            }

            try
            {
                logger.LogDebug("Reading seed data from: {FilePath}", path);
                var json = await File.ReadAllTextAsync(path);
                var data = JsonSerializer.Deserialize<List<T>>(json, JsonOptions);
                if (data == null || !data.Any())
                {
                    logger.LogWarning("No data found in seed file: {FileName}", fileName);
                    return new List<T>();
                }
                logger.LogInformation("Successfully read {Count} records from {FileName}", data.Count, fileName);
                return data;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error reading seed data from {FileName} at path {FilePath}", fileName, path);
                throw;
            }
        }

        private static string? FindSeedDataFile(string fileName, ILogger logger)
        {
            // Strategy 1: Use assembly location as base
            var assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            var assemblyDir = Path.GetDirectoryName(assemblyLocation);
            
            if (!string.IsNullOrEmpty(assemblyDir))
            {
                // Look for SeedData folder relative to assembly location
                var seedDataPaths = new[]
                {
                    Path.Combine(assemblyDir, "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
                };

                foreach (var seedPath in seedDataPaths)
                {
                    var fullPath = Path.GetFullPath(seedPath);
                    logger.LogDebug("Checking seed data path: {Path}", fullPath);
                    if (File.Exists(fullPath))
                    {
                        logger.LogDebug("Found seed data file at: {Path}", fullPath);
                        return fullPath;
                    }
                }
            }

            // Strategy 2: Use current working directory
            var currentDir = Directory.GetCurrentDirectory();
            var workingDirPaths = new[]
            {
                Path.Combine(currentDir, "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(currentDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(currentDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
            };

            foreach (var workingPath in workingDirPaths)
            {
                var fullPath = Path.GetFullPath(workingPath);
                logger.LogDebug("Checking working directory path: {Path}", fullPath);
                if (File.Exists(fullPath))
                {
                    logger.LogDebug("Found seed data file at: {Path}", fullPath);
                    return fullPath;
                }
            }

            // Strategy 3: Search from AppContext.BaseDirectory
            var baseDir = AppContext.BaseDirectory;
            var baseDirPaths = new[]
            {
                Path.Combine(baseDir, "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
            };

            foreach (var basePath in baseDirPaths)
            {
                var fullPath = Path.GetFullPath(basePath);
                logger.LogDebug("Checking base directory path: {Path}", fullPath);
                if (File.Exists(fullPath))
                {
                    logger.LogDebug("Found seed data file at: {Path}", fullPath);
                    return fullPath;
                }
            }

            logger.LogError("Could not locate seed data file: {FileName}. Searched in assembly location: {AssemblyDir}, working directory: {WorkingDir}, base directory: {BaseDir}",
                fileName, assemblyDir, currentDir, baseDir);
            return null;
        }

        private static async Task SeedEntity<T>(
            ApplicationDbContext context,
            ILogger logger,
            string fileName,
            string entityName,
            Func<T, bool> existsCheck,
            Action<T>? beforeAdd = null) where T : class
        {
            try
            {
                logger.LogInformation("Starting to seed {Entity} from {FileName}", entityName, fileName);
                var entities = await ReadJsonData<T>(fileName, logger);
                var addedCount = 0;
                var skippedCount = 0;

                foreach (var entity in entities)
                {
                    try
                    {
                        if (!existsCheck(entity))
                        {
                            beforeAdd?.Invoke(entity);
                            context.Set<T>().Add(entity);
                            addedCount++;
                        }
                        // else (commented out)
                        {
                            skippedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error processing {Entity} record", entityName);
                        throw;
                    }
                }

                await context.SaveChangesAsync();
                logger.LogInformation("Completed seeding {Entity}: {Added} added, {Skipped} skipped", 
                    entityName, addedCount, skippedCount);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error seeding {Entity}", entityName);
                throw;
            }
        }

        // Helper method to check if a table is empty
        private static bool IsTableEmpty<T>(ApplicationDbContext context) where T : class
        {
            return !context.Set<T>().IgnoreQueryFilters().Any();
        }

        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DataSeederLogger>>();
            
            logger.LogInformation("Starting data seeding process...");

            try
            {
                // Try to use the new multi-storage seeding coordinator
                var seedingCoordinator = scope.ServiceProvider.GetService<ISeedingCoordinator>();
                if (seedingCoordinator != null)
                {
                    logger.LogInformation("Using multi-storage seeding coordinator...");
                    await seedingCoordinator.SeedAllStoragesAsync();
                }
                else
                {
                    logger.LogInformation("Multi-storage coordinator not available, falling back to legacy SQL-only seeding...");
                    await SeedUsersAsync(serviceProvider, logger);
                }

                logger.LogInformation("Data seeding process completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred during data seeding");
                
                // If multi-storage seeding fails, try fallback to legacy seeding
                var seedingCoordinator = scope.ServiceProvider.GetService<ISeedingCoordinator>();
                if (seedingCoordinator != null)
                {
                    logger.LogWarning("Multi-storage seeding failed, attempting fallback to legacy SQL-only seeding...");
                    try
                    {
                        await SeedUsersAsync(serviceProvider, logger);
                        logger.LogInformation("Fallback seeding completed successfully");
                    }
                    catch (Exception fallbackEx)
                    {
                        logger.LogError(fallbackEx, "Fallback seeding also failed");
                        throw;
                    }
                }
                else
                {
                    throw;
                }
            }
        }

        private static async Task SeedUsersAsync(IServiceProvider serviceProvider, ILogger logger)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<FY.WB.CSHero2.Infrastructure.Persistence.ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

            try
            {
                // Check if tables are empty before seeding
                bool tenantProfilesEmpty = IsTableEmpty<TenantProfile>(context);
                bool clientsEmpty = IsTableEmpty<Client>(context);
                bool reportsEmpty = IsTableEmpty<Report>(context);
                bool reportSectionsEmpty = IsTableEmpty<ReportSection>(context);
                bool reportSectionFieldsEmpty = IsTableEmpty<ReportSectionField>(context);
                bool invoicesEmpty = IsTableEmpty<Invoice>(context);
                bool formsEmpty = IsTableEmpty<Form>(context);
                bool templatesEmpty = IsTableEmpty<Template>(context);
                bool uploadsEmpty = IsTableEmpty<Upload>(context);

                logger.LogInformation("Table status: TenantProfiles={0}, Clients={1}, Reports={2}, ReportSections={3}, ReportSectionFields={4}, Invoices={5}, Forms={6}, Templates={7}, Uploads={8}",
                    tenantProfilesEmpty ? "Empty" : "Has Data",
                    clientsEmpty ? "Empty" : "Has Data",
                    reportsEmpty ? "Empty" : "Has Data",
                    reportSectionsEmpty ? "Empty" : "Has Data",
                    reportSectionFieldsEmpty ? "Empty" : "Has Data",
                    invoicesEmpty ? "Empty" : "Has Data",
                    formsEmpty ? "Empty" : "Has Data",
                    templatesEmpty ? "Empty" : "Has Data",
                    uploadsEmpty ? "Empty" : "Has Data");

                // Only seed if tables are empty
                if (!tenantProfilesEmpty)
                {
                    logger.LogInformation("Skipping TenantProfiles seeding as table already has data");
                }
                else
                {
                    // --- Seed TenantProfiles with DTO mapping ---
                    logger.LogInformation("Starting to seed TenantProfile from tenant-profiles.json (using DTO mapping)");
                    var tenantProfileDtos = await ReadJsonData<TenantProfileSeedDto>("tenant-profiles.json", logger);
                    var tenantProfilesToSeed = new List<TenantProfile>();

                    foreach (var dto in tenantProfileDtos)
                    {
                        // Assuming TenantProfile has a constructor that takes basic fields and an ID.
                        // Or, if it has a parameterless constructor, initialize and set properties.
                        // Using the public constructor from TenantProfile.cs:
                        var profileEntity = new TenantProfile(
                            dto.Id,
                            dto.Name,
                            dto.Email,
                            dto.Status,
                            dto.Phone,
                            dto.Company,
                            dto.Subscription,
                            dto.LastLoginTime,
                            dto.BillingCycle,
                            dto.NextBillingDate,
                            dto.SubscriptionStatus
                            // Constructor handles PaymentMethodInfo and BillingAddressInfo if passed
                        );

                        // Explicitly set PaymentMethod and BillingAddress as JSON strings
                        if (dto.PaymentMethod != null)
                        {
                            profileEntity.PaymentMethod = JsonSerializer.Serialize(dto.PaymentMethod, JsonOptions);
                        }
                        // else (commented out)
                        {
                            profileEntity.PaymentMethod = "{}"; // Ensure it's a valid JSON object string
                        }

                        if (dto.BillingAddress != null)
                        {
                            profileEntity.BillingAddress = JsonSerializer.Serialize(dto.BillingAddress, JsonOptions);
                        }
                        // else (commented out)
                        {
                            profileEntity.BillingAddress = "{}"; // Ensure it's a valid JSON object string
                        }
                        
                        profileEntity.TenantId = dto.Id; // Set TenantId to match the entity's Id
                        profileEntity.CreationTime = DateTime.UtcNow;
                        profileEntity.LastModificationTime = DateTime.UtcNow;
                        
                        tenantProfilesToSeed.Add(profileEntity);
                    }

                    var addedTenantProfiles = 0;
                    var skippedTenantProfiles = 0;
                    foreach (var profileEntity in tenantProfilesToSeed)
                    {
                        if (!context.TenantProfiles.IgnoreQueryFilters().Any(t => t.Id == profileEntity.Id)) // Check by Id, ignoring global filters
                        {
                            context.TenantProfiles.Add(profileEntity);
                            addedTenantProfiles++;
                            
                            // Save after each tenant profile to ensure it's committed
                            await context.SaveChangesAsync();
                        }
                        // else (commented out)
                        {
                            skippedTenantProfiles++;
                        }
                    }
                    logger.LogInformation("Completed seeding TenantProfile: {Added} added, {Skipped} skipped", 
                        addedTenantProfiles, skippedTenantProfiles);
                    // --- End Seed TenantProfiles ---

                    // Ensure TenantProfiles are committed to the database before proceeding
                    await context.SaveChangesAsync();
                    logger.LogInformation("TenantProfiles committed to database");
                }

                // Seed Clients before Reports (as Reports depend on Clients)
                if (!clientsEmpty)
                {
                    logger.LogInformation("Skipping Clients seeding as table already has data");
                }
                else
                {
                    await SeedEntity<Client>(
                        context, logger,
                        "clients.json",
                        "Client",
                        client => context.Clients.IgnoreQueryFilters().Any(c => c.Id == client.Id), // Check by Id, ignoring global filters
                        client =>
                        {
                            // Set audit properties if not already set
                            if (client.CreationTime == default)
                                client.CreationTime = DateTime.UtcNow;
                            if (client.LastModificationTime == null)
                                client.LastModificationTime = DateTime.UtcNow;
                        }
                    );
                }

                // --- Custom Seeding for Reports to handle FK constraint with ClientName lookup ---
                if (!reportsEmpty)
                {
                    logger.LogInformation("Skipping Reports seeding as table already has data");
                }
                else
                {
                    logger.LogInformation("Starting to seed Report from reports.json (with FK lookup by ClientName and TenantId)");
                    var reportEntitiesFromJson = await ReadJsonData<Report>("reports.json", logger);
                    
                    // Get all clients from the database after they have been seeded
                    var allClientsInDb = await context.Clients.IgnoreQueryFilters().ToListAsync();
                    
                    var reportsToSeed = new List<Report>();
                    var skippedReportsDueToFkLookup = 0;

                    foreach (var reportFromJson in reportEntitiesFromJson)
                    {
                        // Look up the actual client in the database using ClientName (which matches CompanyName) and TenantId
                        var actualClient = allClientsInDb.FirstOrDefault(c => 
                            c.CompanyName == reportFromJson.ClientName && 
                            c.TenantId == reportFromJson.TenantId);

                        if (actualClient == null)
                        {
                            logger.LogWarning(
                                "Skipping Report '{ReportName}' (JSON ID: {JsonReportId}) for JSON ClientName '{JsonClientName}' and TenantId {JsonTenantId} because a matching client was not found in the database.", 
                                reportFromJson.Name, 
                                reportFromJson.Id,
                                reportFromJson.ClientName, 
                                reportFromJson.TenantId);
                            skippedReportsDueToFkLookup++;
                            continue;
                        }

                        // Create a new report entity with the correct ClientId from the database using constructor
                        var reportEntity = new Report(
                            reportFromJson.Id, // Use the Report's ID from JSON
                            reportFromJson.ReportNumber,
                            actualClient.Id, // CRITICAL: Use the ID from the client found in the DB
                            reportFromJson.ClientName,
                            reportFromJson.Name,
                            reportFromJson.Category,
                            reportFromJson.SlideCount,
                            reportFromJson.Status,
                            reportFromJson.Author
                        );
                        
                        // Set additional properties
                        reportEntity.TenantId = reportFromJson.TenantId;
                        reportEntity.CreationTime = reportFromJson.CreationTime == default ? DateTime.UtcNow : reportFromJson.CreationTime;
                        reportEntity.LastModificationTime = reportFromJson.LastModificationTime ?? DateTime.UtcNow;
                        reportEntity.IsDeleted = reportFromJson.IsDeleted;
                        
                        reportsToSeed.Add(reportEntity);
                    }

                    var addedReports = 0;
                    var skippedReportsPk = 0;
                    foreach (var reportEntity in reportsToSeed)
                    {
                        if (!context.Reports.IgnoreQueryFilters().Any(r => r.Id == reportEntity.Id))
                        {
                            context.Reports.Add(reportEntity);
                            addedReports++;
                        }
                        // else (commented out)
                        {
                            skippedReportsPk++;
                        }
                    }
                    await context.SaveChangesAsync();
                    logger.LogInformation("Completed seeding Report: {Added} added, {SkippedPK} skipped (PK violation), {SkippedFKLookup} skipped (Client lookup failed)", 
                        addedReports, skippedReportsPk, skippedReportsDueToFkLookup);
                }
                // --- End Custom Seeding for Reports ---

                // Seed ReportSections (depends on Reports)
                if (!reportSectionsEmpty)
                {
                    logger.LogInformation("Skipping ReportSections seeding as table already has data");
                }
                else
                {
                    try
                    {
                        await SeedEntity<ReportSection>(
                            context, logger,
                            "report-sections.json",
                            "ReportSection",
                            section => context.ReportSections.IgnoreQueryFilters().Any(s => s.Id == section.Id),
                            section =>
                            {
                                // Set audit properties if not already set
                                if (section.CreationTime == default)
                                    section.CreationTime = DateTime.UtcNow;
                                if (section.LastModificationTime == null)
                                    section.LastModificationTime = DateTime.UtcNow;
                            }
                        );
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error seeding ReportSection");
                        // Continue with other entities instead of throwing
                    }
                }

                // Seed ReportSectionFields (depends on ReportSections)
                if (!reportSectionFieldsEmpty)
                {
                    logger.LogInformation("Skipping ReportSectionFields seeding as table already has data");
                }
                else
                {
                    try
                    {
                        await SeedEntity<ReportSectionField>(
                            context, logger,
                            "report-section-fields.json",
                            "ReportSectionField",
                            field => context.ReportSectionFields.IgnoreQueryFilters().Any(f => f.Id == field.Id),
                            field =>
                            {
                                // Set audit properties if not already set
                                if (field.CreationTime == default)
                                    field.CreationTime = DateTime.UtcNow;
                                if (field.LastModificationTime == null)
                                    field.LastModificationTime = DateTime.UtcNow;
                            }
                        );
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error seeding ReportSectionField");
                        // Continue with other entities instead of throwing
                    }
                }

                // Seed Invoices
                if (!invoicesEmpty)
                {
                    logger.LogInformation("Skipping Invoices seeding as table already has data");
                }
                else
                {
                    try
                    {
                        await SeedEntity<Invoice>(
                            context, logger,
                            "invoices.json",
                            "Invoice",
                            invoice => context.Invoices.IgnoreQueryFilters().Any(i => i.Id == invoice.Id), // Check by Id, ignoring global filters
                            invoice =>
                            {
                                // Set audit properties if not already set
                                if (invoice.CreationTime == default)
                                    invoice.CreationTime = DateTime.UtcNow;
                                if (invoice.LastModificationTime == null)
                                    invoice.LastModificationTime = DateTime.UtcNow;
                            }
                        );
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error seeding Invoice");
                        // Continue with other entities instead of throwing
                    }
                }

                // Seed Forms
                if (!formsEmpty)
                {
                    logger.LogInformation("Skipping Forms seeding as table already has data");
                }
                else
                {
                    try
                    {
                        await SeedEntity<Form>(
                            context, logger,
                            "forms.json",
                            "Form",
                            form => context.Forms.IgnoreQueryFilters().Any(f => f.Id == form.Id), // Check by Id, ignoring global filters
                            form =>
                            {
                                // Set audit properties if not already set
                                if (form.CreationTime == default)
                                    form.CreationTime = DateTime.UtcNow;
                                if (form.LastModificationTime == null)
                                    form.LastModificationTime = DateTime.UtcNow;
                            }
                        );
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error seeding Form");
                        // Continue with other entities instead of throwing
                    }
                }

                // --- Custom Seeding for Templates to handle complex properties ---
                if (!templatesEmpty)
                {
                    logger.LogInformation("Skipping Templates seeding as table already has data");
                }
                else
                {
                    try
                    {
                        logger.LogInformation("Starting to seed Template from templates.json (using DTO mapping)");
                        var templateDtos = await ReadJsonData<TemplateSeedDto>("templates.json", logger);
                        var templatesToSeed = new List<Template>();

                        foreach (var dto in templateDtos)
                        {
                            var templateEntity = new Template(
                                dto.Id,
                                dto.Name,
                                dto.Description,
                                dto.Category,
                                dto.ThumbnailUrl
                                // Constructor will call SetTags, SetSections, SetFields if DTO properties are passed
                            );

                            if (dto.Tags != null)
                                templateEntity.Tags = JsonSerializer.Serialize(dto.Tags, JsonOptions);
                            // else (commented out)
                                templateEntity.Tags = "[]";

                            if (dto.Sections != null)
                                templateEntity.Sections = JsonSerializer.Serialize(dto.Sections, JsonOptions);
                            // else (commented out)
                                templateEntity.Sections = "[]";
                            
                            if (dto.Fields != null)
                                templateEntity.Fields = JsonSerializer.Serialize(dto.Fields, JsonOptions);
                            // else (commented out)
                                templateEntity.Fields = "[]";

                            templateEntity.TenantId = dto.TenantId;
                            templateEntity.CreationTime = DateTime.UtcNow;
                            templateEntity.LastModificationTime = DateTime.UtcNow;

                            templatesToSeed.Add(templateEntity);
                        }

                        var addedTemplates = 0;
                        var skippedTemplates = 0;
                        foreach (var templateEntity in templatesToSeed)
                        {
                            if (!context.Templates.IgnoreQueryFilters().Any(t => t.Id == templateEntity.Id))
                            {
                                context.Templates.Add(templateEntity);
                                addedTemplates++;
                            }
                            // else (commented out)
                            {
                                skippedTemplates++;
                            }
                        }
                        await context.SaveChangesAsync();
                        logger.LogInformation("Completed seeding Template: {Added} added, {Skipped} skipped", 
                            addedTemplates, skippedTemplates);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error seeding Template");
                        // Continue with other entities instead of throwing
                    }
                }
                // --- End Custom Seeding for Templates ---
                
                // Seed Uploads
                if (!uploadsEmpty)
                {
                    logger.LogInformation("Skipping Uploads seeding as table already has data");
                }
                else
                {
                    try
                    {
                        await SeedEntity<Upload>(
                            context, logger,
                            "uploads.json",
                            "Upload",
                            upload => context.Uploads.IgnoreQueryFilters().Any(u => u.Id == upload.Id), // Check by Id, ignoring global filters
                            upload =>
                            {
                                // Set audit properties if not already set
                                if (upload.CreationTime == default)
                                    upload.CreationTime = DateTime.UtcNow;
                                if (upload.LastModificationTime == null)
                                    upload.LastModificationTime = DateTime.UtcNow;
                            }
                        );
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error seeding Upload");
                        // Continue with other entities instead of throwing
                    }
                }

                // Check if AspNetUsers table is empty before seeding users
                bool usersEmpty = !userManager.Users.Any();
                logger.LogInformation("AspNetUsers table status: {0}", usersEmpty ? "Empty" : "Has Data");

                if (!usersEmpty)
                {
                    logger.LogInformation("Skipping user seeding as AspNetUsers table already has data");
                }
                else
                {
                    // Get the tenant store and configuration for tenant creation
                    var tenantStore = scope.ServiceProvider.GetService<IMultiTenantStore<FY.WB.CSHero2.Infrastructure.Persistence.AppTenantInfo>>();
                    var configuration = scope.ServiceProvider.GetService<IConfiguration>();
                    
                    if (tenantStore == null || configuration == null)
                    {
                        logger.LogWarning("Tenant store or configuration is null. Skipping user seeding.");
                    }
                    // else (commented out)
                    {
                        // Seed admin user if not exists
                        var adminEmail = "<EMAIL>";
                        var adminUser = await userManager.FindByEmailAsync(adminEmail);
                        if (adminUser == null)
                        {
                            logger.LogInformation("Creating admin user...");
                            adminUser = new FY.WB.CSHero2.Infrastructure.Persistence.ApplicationUser
                            {
                                UserName = adminEmail,
                                Email = adminEmail,
                                EmailConfirmed = true,
                                IsAdmin = true,
                                CreationTime = DateTime.UtcNow
                            };
                            var result = await userManager.CreateAsync(adminUser, "AdminPass123!");
                            if (result.Succeeded)
                            {
                                logger.LogInformation("Admin user created successfully");
                            }
                            // else (commented out)
                            {
                                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                                logger.LogError("Failed to create admin user: {Errors}", errors);
                            }
                        }
                        // else (commented out)
                        {
                            logger.LogInformation("Admin user already exists");
                        }
                        
                        // Ensure Admin role exists
                        string adminRoleName = "Admin";
                        if (!await roleManager.RoleExistsAsync(adminRoleName))
                        {
                            logger.LogInformation("'{RoleName}' role does not exist. Creating...", adminRoleName);
                            var roleResult = await roleManager.CreateAsync(new IdentityRole<Guid>(adminRoleName));
                            if (roleResult.Succeeded)
                            {
                                logger.LogInformation("'{RoleName}' role created successfully.", adminRoleName);
                            }
                            // else (commented out)
                            {
                                logger.LogError("Failed to create '{RoleName}' role: {Errors}", adminRoleName, string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                            }
                        }
                        // else (commented out)
                        {
                            logger.LogInformation("'{RoleName}' role already exists.", adminRoleName);
                        }
                        
                        // Ensure admin user is in Admin role
                        if (adminUser != null)
                        {
                            if (!await userManager.IsInRoleAsync(adminUser, adminRoleName))
                            {
                                logger.LogInformation("Adding admin user '{UserName}' to '{RoleName}' role.", adminUser.UserName, adminRoleName);
                                var addToRoleResult = await userManager.AddToRoleAsync(adminUser, adminRoleName);
                                if (addToRoleResult.Succeeded)
                                {
                                    logger.LogInformation("Admin user '{UserName}' added to '{RoleName}' role successfully.", adminUser.UserName, adminRoleName);
                                }
                                // else (commented out)
                                {
                                    logger.LogError("Failed to add admin user '{UserName}' to '{RoleName}' role: {Errors}", adminUser.UserName, adminRoleName, string.Join(", ", addToRoleResult.Errors.Select(e => e.Description)));
                                }
                            }
                            // else (commented out)
                            {
                                logger.LogInformation("Admin user '{UserName}' is already in '{RoleName}' role.", adminUser.UserName, adminRoleName);
                            }
                        }
                        // else (commented out)
                        {
                            logger.LogWarning("Admin user object is null, cannot assign to '{RoleName}' role.", adminRoleName);
                        }
                        
                        // Seed users for each tenant profile in tenant-profiles.json
                        await CreateUsersForTenantProfiles(
                            userManager,
                            roleManager,
                            tenantStore,
                            configuration,
                            context,
                            logger);
                    }
                }

                // Note: CosmosDB seeding is now handled in Program.cs after all services are registered
                // to avoid circular dependency issues between Infrastructure and ReportRenderingEngine projects
                logger.LogInformation("SQL Server data seeding completed. CosmosDB seeding will be handled separately.");

                logger.LogInformation("Data seeding process completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred during data seeding");
                throw;
            }
        }
        
        private static async Task CreateUsersForTenantProfiles(
            UserManager<FY.WB.CSHero2.Infrastructure.Persistence.ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            IMultiTenantStore<FY.WB.CSHero2.Infrastructure.Persistence.AppTenantInfo> tenantStore,
            IConfiguration configuration,
            ApplicationDbContext context,
            ILogger logger)
        {
            try
            {
                // Get all tenant profiles from the database
                var tenantProfiles = await context.TenantProfiles.IgnoreQueryFilters().ToListAsync();
                
                if (!tenantProfiles.Any())
                {
                    logger.LogWarning("No tenant profiles found in the database for user creation");
                    return;
                }
                
                logger.LogInformation("Creating users for {Count} tenant profiles", tenantProfiles.Count);
                
                // Make sure the User role exists
                string userRoleName = "User";
                if (!await roleManager.RoleExistsAsync(userRoleName))
                {
                    logger.LogInformation("'{RoleName}' role does not exist. Creating...", userRoleName);
                    var roleResult = await roleManager.CreateAsync(new IdentityRole<Guid>(userRoleName));
                    if (roleResult.Succeeded)
                    {
                        logger.LogInformation("'{RoleName}' role created successfully.", userRoleName);
                    }
                    // else (commented out)
                    {
                        logger.LogError("Failed to create '{RoleName}' role: {Errors}", userRoleName, 
                            string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                        return;
                    }
                }
                
                foreach (var profile in tenantProfiles)
                {
                    // Use the email from the tenant profile, or create one if it doesn't have one
                    string email = !string.IsNullOrEmpty(profile.Email) 
                        ? profile.Email 
                        : $"user@{profile.Company.ToLower().Replace(" ", "")}.com";
                    
                    // Skip if user already exists
                    if (await userManager.FindByEmailAsync(email) != null)
                    {
                        logger.LogInformation("User already exists for tenant profile: {Email}", email);
                        continue;
                    }
                    
                    // Create AppTenantInfo for Finbuckle
                    var identifier = $"{profile.Company.ToLower().Replace(" ", "-")}-{Guid.NewGuid().ToString()[..8]}";
                    var newTenant = new FY.WB.CSHero2.Infrastructure.Persistence.AppTenantInfo
                    {
                        Id = profile.Id.ToString(),  // Use the ID of the TenantProfile
                        Identifier = identifier,
                        Name = profile.Company,
                        ConnectionString = configuration.GetConnectionString("DefaultConnection")
                    };
                    
                    // Add tenant to Finbuckle store if not already present
                    if (await tenantStore.TryGetAsync(profile.Id.ToString()) == null)
                    {
                        var addResult = await tenantStore.TryAddAsync(newTenant);
                        if (!addResult)
                        {
                            logger.LogError("Failed to add tenant to store for {Email}", email);
                            continue;
                        }
                    }
                    
                    // Create user with TenantId matching the TenantProfile Id
                    var tenantUser = new FY.WB.CSHero2.Infrastructure.Persistence.ApplicationUser
                    {
                        UserName = email,
                        Email = email,
                        EmailConfirmed = true,
                        IsAdmin = false,
                        CompanyName = profile.Company,
                        CompanyUrl = $"https://{profile.Company.ToLower().Replace(" ", "")}.com",
                        TenantId = profile.Id,  // This is the crucial link
                        CreationTime = DateTime.UtcNow
                    };
                    
                    // Standard password for all test users - would be different in production
                    var result = await userManager.CreateAsync(tenantUser, "Test123!");
                    if (result.Succeeded)
                    {
                        logger.LogInformation("User created successfully for tenant profile: {Email} (TenantId: {TenantId})", 
                            email, profile.Id);
                        
                        // Assign to User role
                        var addToRoleResult = await userManager.AddToRoleAsync(tenantUser, userRoleName);
                        if (addToRoleResult.Succeeded)
                        {
                            logger.LogInformation("User '{Email}' added to '{RoleName}' role successfully.", email, userRoleName);
                        }
                        // else (commented out)
                        {
                            logger.LogError("Failed to add user '{Email}' to '{RoleName}' role: {Errors}", 
                                email, userRoleName, string.Join(", ", addToRoleResult.Errors.Select(e => e.Description)));
                        }
                    }
                    // else (commented out)
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        logger.LogError("Failed to create user for tenant profile {Email}: {Errors}", email, errors);
                    }
                }
                
                logger.LogInformation("Completed creating users for tenant profiles");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred creating users for tenant profiles");
                throw;
            }
        }
    }
}
