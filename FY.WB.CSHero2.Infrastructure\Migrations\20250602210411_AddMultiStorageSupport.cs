﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddMultiStorageSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReportVersions_CreatedAt",
                table: "ReportVersions");

            migrationBuilder.DropIndex(
                name: "IX_ComponentDefinitions_GeneratedAt",
                table: "ComponentDefinitions");

            migrationBuilder.AddColumn<string>(
                name: "StyleDocumentId",
                table: "Templates",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ComponentsBlobId",
                table: "ReportVersions",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataBlobPath",
                table: "ReportVersions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataDocumentId",
                table: "ReportVersions",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDataInBlob",
                table: "ReportVersions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "StyleDocumentId",
                table: "ReportVersions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ComponentsBlobId",
                table: "Reports",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataDocumentId",
                table: "Reports",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AssetBlobPath",
                table: "ComponentDefinitions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StyleDocumentId",
                table: "ComponentDefinitions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ReportStyles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Theme = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "modern"),
                    ColorScheme = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "blue"),
                    Typography = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "sans-serif"),
                    Spacing = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "normal"),
                    LayoutOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    TypographyOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    StructureOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    ContentOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    VisualOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportStyles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportStyles_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_CreationTime",
                table: "ReportVersions",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_CreationTime",
                table: "ComponentDefinitions",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStyles_ColorScheme",
                table: "ReportStyles",
                column: "ColorScheme");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStyles_CreationTime",
                table: "ReportStyles",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStyles_ReportId",
                table: "ReportStyles",
                column: "ReportId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReportStyles_Theme",
                table: "ReportStyles",
                column: "Theme");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReportStyles");

            migrationBuilder.DropIndex(
                name: "IX_ReportVersions_CreationTime",
                table: "ReportVersions");

            migrationBuilder.DropIndex(
                name: "IX_ComponentDefinitions_CreationTime",
                table: "ComponentDefinitions");

            migrationBuilder.DropColumn(
                name: "StyleDocumentId",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "ComponentsBlobId",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "DataBlobPath",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "DataDocumentId",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "IsDataInBlob",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "StyleDocumentId",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "ComponentsBlobId",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "DataDocumentId",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "AssetBlobPath",
                table: "ComponentDefinitions");

            migrationBuilder.DropColumn(
                name: "StyleDocumentId",
                table: "ComponentDefinitions");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_CreatedAt",
                table: "ReportVersions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_GeneratedAt",
                table: "ComponentDefinitions",
                column: "GeneratedAt");
        }
    }
}
